/**
 * @file 编码器使用示例代码.c
 * @brief 步进电机编码器数据接收系统的完整使用示例
 * @copyright 白蛋电子工作室
 */

#include "app_uasrt.h"
#include "app_motor.h"
#include "main.h"

// 全局变量
volatile uint8_t system_ready = 0;
volatile uint8_t move_in_progress = 0;

/**
 * @brief 系统初始化示例
 */
void encoder_system_init_example(void)
{
    // 1. 初始化电机系统
    Motor_Init();
    
    // 2. 启动UART中断接收
    HAL_UART_Receive_IT(&huart4, &motor_x_rx_byte, 1);
    HAL_UART_Receive_IT(&huart5, &motor_y_rx_byte, 1);
    
    // 3. 清除所有标志
    clear_motor_ready_flags();
    
    // 4. 系统就绪
    system_ready = 1;
    my_printf(&huart1, "编码器系统初始化完成！\r\n");
}

/**
 * @brief 基础使用示例 - 单点移动
 */
void basic_move_example(void)
{
    if (!system_ready) {
        my_printf(&huart1, "系统未初始化！\r\n");
        return;
    }
    
    my_printf(&huart1, "开始基础移动示例...\r\n");
    
    // 1. 清除之前的状态
    clear_motor_ready_flags();
    
    // 2. 发送移动命令 (X轴1000脉冲，Y轴1500脉冲)
    Motor_Set_Position(1000, 1500);
    move_in_progress = 1;
    
    // 3. 等待到位
    uint32_t start_time = HAL_GetTick();
    while (move_in_progress)
    {
        uint8_t status = check_motors_ready_for_next_step();
        
        switch (status)
        {
            case 1: // 到位
                my_printf(&huart1, "电机已到位！\r\n");
                move_in_progress = 0;
                break;
                
            case 2: // 错误
                my_printf(&huart1, "检测到错误！\r\n");
                move_in_progress = 0;
                break;
                
            case 0: // 继续等待
                if ((HAL_GetTick() - start_time) > 10000) // 10秒超时
                {
                    my_printf(&huart1, "移动超时！\r\n");
                    move_in_progress = 0;
                }
                break;
        }
        
        HAL_Delay(50); // 避免过度轮询
    }
}

/**
 * @brief 高级使用示例 - 轨迹跟踪
 */
void trajectory_tracking_example(void)
{
    // 定义一个简单的矩形轨迹
    typedef struct {
        int32_t x;
        int32_t y;
        const char* description;
    } trajectory_point_t;
    
    trajectory_point_t trajectory[] = {
        {0,    0,    "起始点"},
        {2000, 0,    "右下角"},
        {2000, 2000, "右上角"},
        {0,    2000, "左上角"},
        {0,    0,    "回到起始点"}
    };
    
    int num_points = sizeof(trajectory) / sizeof(trajectory[0]);
    
    my_printf(&huart1, "开始轨迹跟踪示例 - 矩形路径\r\n");
    
    for (int i = 0; i < num_points; i++)
    {
        my_printf(&huart1, "移动到第%d点: %s (X=%d, Y=%d)\r\n", 
                  i+1, trajectory[i].description, trajectory[i].x, trajectory[i].y);
        
        // 清除标志
        clear_motor_ready_flags();
        
        // 发送命令
        Motor_Set_Position(trajectory[i].x, trajectory[i].y);
        
        // 等待完成
        uint32_t start_time = HAL_GetTick();
        while (1)
        {
            uint8_t status = check_motors_ready_for_next_step();
            
            if (status == 1)
            {
                my_printf(&huart1, "到达第%d点\r\n", i+1);
                break;
            }
            else if (status == 2)
            {
                my_printf(&huart1, "第%d点移动失败！\r\n", i+1);
                return;
            }
            else if ((HAL_GetTick() - start_time) > 15000)
            {
                my_printf(&huart1, "第%d点移动超时！\r\n", i+1);
                return;
            }
            
            HAL_Delay(100);
        }
        
        // 在每个点停留0.5秒
        HAL_Delay(500);
    }
    
    my_printf(&huart1, "轨迹跟踪完成！\r\n");
}

/**
 * @brief 实时状态监控示例
 */
void status_monitoring_example(void)
{
    my_printf(&huart1, "开始状态监控示例...\r\n");
    
    // 发送一个移动命令用于演示
    clear_motor_ready_flags();
    Motor_Set_Position(1500, 1000);
    
    uint32_t last_status_time = 0;
    uint32_t start_time = HAL_GetTick();
    
    while (1)
    {
        uint32_t current_time = HAL_GetTick();
        
        // 每500ms输出一次状态
        if ((current_time - last_status_time) >= 500)
        {
            uint8_t x_status = get_motor_ready_status(1);
            uint8_t y_status = get_motor_ready_status(2);
            uint8_t overall_status = check_motors_ready_for_next_step();
            
            my_printf(&huart1, "[%lu ms] X轴:%s, Y轴:%s, 整体状态:%d\r\n",
                      current_time - start_time,
                      x_status ? "到位" : "运动中",
                      y_status ? "到位" : "运动中",
                      overall_status);
            
            last_status_time = current_time;
            
            // 检查是否完成
            if (overall_status == 1)
            {
                my_printf(&huart1, "监控完成 - 所有电机已到位！\r\n");
                break;
            }
            else if (overall_status == 2)
            {
                my_printf(&huart1, "监控完成 - 检测到错误！\r\n");
                break;
            }
        }
        
        // 超时保护
        if ((current_time - start_time) > 20000)
        {
            my_printf(&huart1, "监控超时！\r\n");
            break;
        }
        
        HAL_Delay(10);
    }
}

/**
 * @brief 画图应用示例
 */
void drawing_application_example(void)
{
    my_printf(&huart1, "开始画图应用示例...\r\n");
    
    // 定义绘图点 (使用浮点坐标)
    float drawing_points[][2] = {
        {10.5, 10.5},   // 起始点
        {50.0, 10.5},   // 水平线
        {50.0, 50.0},   // 垂直线
        {10.5, 50.0},   // 水平线
        {10.5, 10.5}    // 回到起始点
    };
    
    int num_points = sizeof(drawing_points) / sizeof(drawing_points[0]);
    
    for (int i = 0; i < num_points; i++)
    {
        my_printf(&huart1, "绘制到点%d: X=%.1f, Y=%.1f\r\n", 
                  i+1, drawing_points[i][0], drawing_points[i][1]);
        
        // 使用专门的画图控制函数
        uint8_t result;
        uint32_t retry_count = 0;
        const uint32_t max_retries = 100;
        
        do {
            result = drawing_interpolation_control(drawing_points[i][0], drawing_points[i][1]);
            
            switch (result)
            {
                case 1: // 运动开始
                    my_printf(&huart1, "开始移动到点%d\r\n", i+1);
                    break;
                    
                case 0: // 等待电机到位
                    HAL_Delay(50);
                    retry_count++;
                    break;
                    
                case 2: // 错误状态
                    my_printf(&huart1, "绘制过程中发生错误！\r\n");
                    return;
            }
            
            if (retry_count >= max_retries)
            {
                my_printf(&huart1, "点%d绘制超时！\r\n", i+1);
                return;
            }
            
        } while (result == 0);
        
        if (result == 1)
        {
            my_printf(&huart1, "完成点%d的绘制\r\n", i+1);
        }
    }
    
    my_printf(&huart1, "画图示例完成！\r\n");
}

/**
 * @brief 错误处理示例
 */
void error_handling_example(void)
{
    my_printf(&huart1, "开始错误处理示例...\r\n");
    
    // 模拟一个可能出错的操作
    clear_motor_ready_flags();
    Motor_Set_Position(5000, 5000); // 较大的移动距离
    
    uint32_t start_time = HAL_GetTick();
    uint8_t error_handled = 0;
    
    while (!error_handled)
    {
        uint8_t status = check_motors_ready_for_next_step();
        uint32_t elapsed_time = HAL_GetTick() - start_time;
        
        switch (status)
        {
            case 1: // 正常完成
                my_printf(&huart1, "操作正常完成\r\n");
                error_handled = 1;
                break;
                
            case 2: // 检测到错误
                my_printf(&huart1, "检测到通信错误，尝试恢复...\r\n");
                
                // 错误恢复步骤
                Motor_Stop(); // 停止电机
                HAL_Delay(1000); // 等待1秒
                clear_motor_ready_flags(); // 清除错误标志
                
                // 重新初始化通信
                HAL_UART_Receive_IT(&huart4, &motor_x_rx_byte, 1);
                HAL_UART_Receive_IT(&huart5, &motor_y_rx_byte, 1);
                
                my_printf(&huart1, "错误恢复完成，系统重新就绪\r\n");
                error_handled = 1;
                break;
                
            case 0: // 继续等待
                if (elapsed_time > 30000) // 30秒超时
                {
                    my_printf(&huart1, "操作超时，执行超时处理...\r\n");
                    Motor_Stop();
                    clear_motor_ready_flags();
                    error_handled = 1;
                }
                break;
        }
        
        HAL_Delay(100);
    }
}

/**
 * @brief 主函数示例 - 演示如何集成所有功能
 */
void main_integration_example(void)
{
    my_printf(&huart1, "\r\n=== 步进电机编码器系统演示开始 ===\r\n");
    
    // 1. 系统初始化
    encoder_system_init_example();
    HAL_Delay(1000);
    
    // 2. 基础移动测试
    my_printf(&huart1, "\r\n--- 基础移动测试 ---\r\n");
    basic_move_example();
    HAL_Delay(2000);
    
    // 3. 状态监控测试
    my_printf(&huart1, "\r\n--- 状态监控测试 ---\r\n");
    status_monitoring_example();
    HAL_Delay(2000);
    
    // 4. 轨迹跟踪测试
    my_printf(&huart1, "\r\n--- 轨迹跟踪测试 ---\r\n");
    trajectory_tracking_example();
    HAL_Delay(2000);
    
    // 5. 画图应用测试
    my_printf(&huart1, "\r\n--- 画图应用测试 ---\r\n");
    drawing_application_example();
    HAL_Delay(2000);
    
    // 6. 错误处理测试
    my_printf(&huart1, "\r\n--- 错误处理测试 ---\r\n");
    error_handling_example();
    
    my_printf(&huart1, "\r\n=== 所有演示完成 ===\r\n");
}
