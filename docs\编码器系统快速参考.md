# 步进电机编码器系统快速参考

## 🚀 快速开始 (3步搞定)

### 1️⃣ 初始化
```c
Motor_Init();
HAL_UART_Receive_IT(&huart4, &motor_x_rx_byte, 1);
HAL_UART_Receive_IT(&huart5, &motor_y_rx_byte, 1);
```

### 2️⃣ 发送命令
```c
clear_motor_ready_flags();
Motor_Set_Position(1000, 2000); // X轴1000脉冲，Y轴2000脉冲
```

### 3️⃣ 等待完成
```c
while(check_motors_ready_for_next_step() == 0) {
    HAL_Delay(10);
}
```

## 📋 核心API速查

| 函数 | 功能 | 返回值 |
|------|------|--------|
| `check_motors_ready_for_next_step()` | 检查电机到位状态 | 1=到位, 0=运动中, 2=错误 |
| `clear_motor_ready_flags()` | 清除到位标志 | 无 |
| `get_motor_ready_status(motor_id)` | 获取单个电机状态 | 1=到位, 0=运动中 |
| `drawing_interpolation_control(x,y)` | 画图插值控制 | 1=开始, 0=等待, 2=错误 |

## 🔧 常用代码片段

### 安全移动函数
```c
uint8_t safe_move(int32_t x, int32_t y) {
    clear_motor_ready_flags();
    Motor_Set_Position(x, y);
    
    uint32_t start = HAL_GetTick();
    while(1) {
        uint8_t status = check_motors_ready_for_next_step();
        if(status == 1) return 1;      // 成功
        if(status == 2) return 0;      // 错误
        if((HAL_GetTick() - start) > 10000) return 0; // 超时
        HAL_Delay(10);
    }
}
```

### 状态监控
```c
void print_status() {
    uint8_t x = get_motor_ready_status(1);
    uint8_t y = get_motor_ready_status(2);
    my_printf(&huart1, "X:%s Y:%s\r\n", 
              x?"到位":"运动", y?"到位":"运动");
}
```

### 批量移动
```c
int32_t points[][2] = {{100,200}, {300,400}, {500,600}};
for(int i=0; i<3; i++) {
    if(!safe_move(points[i][0], points[i][1])) {
        my_printf(&huart1, "移动失败!\r\n");
        break;
    }
}
```

## ⚙️ 重要配置

### 数据帧格式
- **起始字节**: `0x01` (X轴) / `0x02` (Y轴)
- **到位标识**: `0xFD 0x9F`
- **帧尾字节**: `0x6B`

### 硬件连接
- **X轴电机**: UART4 (115200bps)
- **Y轴电机**: UART5 (115200bps)
- **调试输出**: UART1

### 关键变量
```c
extern uint8_t motor01_ready;  // X轴到位标志
extern uint8_t motor02_ready;  // Y轴到位标志
extern uint8_t stop_flag_car;  // 系统状态 (0:正常, 1:到位, 2:错误)
```

## 🐛 调试技巧

### 启用调试输出
```c
// 在 app_uasrt.c 中修改
#define DEBUG_ENABLE 1
```

### 常见问题检查清单
- [ ] UART中断是否启用？
- [ ] 波特率是否正确 (115200)？
- [ ] 电机地址是否匹配？
- [ ] 是否调用了 `clear_motor_ready_flags()`？
- [ ] 是否有超时保护？

### 错误代码含义
| 错误码 | 含义 | 解决方案 |
|--------|------|----------|
| 0 | 电机运动中 | 继续等待 |
| 1 | 电机已到位 | 可以发送下一个命令 |
| 2 | 通信错误 | 检查连接，重新初始化 |

## 📊 性能优化

### 减少CPU占用
```c
// 使用定时器检查，而不是连续轮询
multiTimerStart(&check_timer, 50, check_callback, NULL);
```

### 批量处理
```c
// 一次性发送多个命令，减少通信开销
for(int i=0; i<count; i++) {
    Motor_Set_Position(commands[i].x, commands[i].y);
    wait_for_completion();
}
```

## 🔄 状态机模式

```c
typedef enum {
    STATE_IDLE,
    STATE_MOVING, 
    STATE_WAITING,
    STATE_ERROR
} motor_state_t;

motor_state_t current_state = STATE_IDLE;

void state_machine_update() {
    switch(current_state) {
        case STATE_IDLE:
            if(new_command) {
                send_move_command();
                current_state = STATE_MOVING;
            }
            break;
            
        case STATE_MOVING:
            uint8_t status = check_motors_ready_for_next_step();
            if(status == 1) current_state = STATE_IDLE;
            else if(status == 2) current_state = STATE_ERROR;
            break;
            
        case STATE_ERROR:
            handle_error();
            current_state = STATE_IDLE;
            break;
    }
}
```

## 🎯 最佳实践

### ✅ 推荐做法
- 总是在发送新命令前调用 `clear_motor_ready_flags()`
- 使用超时保护避免无限等待
- 定期检查系统状态而不是连续轮询
- 在错误发生时停止电机并重置系统

### ❌ 避免做法
- 不要在中断中执行耗时操作
- 不要忘记重新启动UART接收中断
- 不要在没有超时保护的情况下等待
- 不要忽略错误状态的处理

## 📞 技术支持

### 调试命令 (通过UART1发送)
- `reset` - 返回初始位置
- `set(x,y)` - 设置目标位置

### 日志输出示例
```
[1234 ms] X轴:运动中, Y轴:运动中, 整体状态:0
[1784 ms] X轴:到位, Y轴:运动中, 整体状态:0  
[2156 ms] X轴:到位, Y轴:到位, 整体状态:1
```

---
**版本**: v1.0 | **更新**: 2024年 | **支持**: 白蛋电子工作室
