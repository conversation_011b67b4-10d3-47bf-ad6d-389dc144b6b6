# 电机到位检测系统使用说明

## 概述

本系统实现了基于单字节中断接收的电机到位状态检测，用于画图时的精确插值运动控制。系统通过状态机解析电机反馈数据，判断电机是否到达指定位置，确保画图过程中的精确控制。

## 主要特性

1. **单字节中断接收**：改进了原有的 DMA 空闲中断方式，实现逐字节解析
2. **状态机解析**：三状态解析机制，确保数据帧的完整性和准确性
3. **电机到位检测**：实时监测 X 轴和 Y 轴电机的到位状态
4. **插值运动控制**：提供画图时的精确运动控制接口
5. **错误处理**：完善的错误检测和处理机制

## 系统架构

### 数据帧格式

```
起始字节: 0x01 (X轴) / 0x02 (Y轴)
到位标识: 0xFD 0x9F
帧尾字节: 0x6B
```

### 状态机流程

```
状态0: 等待起始字节 → 状态1: 接收数据内容 → 状态2: 校验数据帧并处理
```

## 核心函数说明

### 1. 电机数据接收处理函数

```c
void Motor_X_Receive_Data(uint8_t com_data);  // X轴电机数据处理
void Motor_Y_Receive_Data(uint8_t com_data);  // Y轴电机数据处理
```

### 2. 电机到位状态检查函数

```c
uint8_t check_motors_ready_for_next_step(void);
// 返回值: 1-所有电机到位, 0-电机未到位, 2-错误状态
```

### 3. 电机状态控制函数

```c
void clear_motor_ready_flags(void);           // 清除到位标志
uint8_t get_motor_ready_status(uint8_t motor_id); // 获取指定电机状态
```

### 4. 画图插值运动控制函数

```c
uint8_t drawing_interpolation_control(float target_x, float target_y);
// 返回值: 1-运动开始, 0-等待电机到位, 2-错误状态
```

## 使用方法

### 1. 初始化配置

在 STM32CubeMX 中：

1. 删除 UART4 和 UART5 的 DMA 配置
2. 启用 UART4 和 UART5 的全局中断
3. 重新生成代码

### 2. 画图控制流程示例

```c
// 画图主循环示例
void drawing_main_loop(void)
{
    float target_points[][2] = {
        {10.0, 20.0},
        {30.0, 40.0},
        {50.0, 60.0}
    };

    static uint8_t current_point = 0;
    static uint8_t drawing_state = 0; // 0: 准备, 1: 运动中, 2: 完成

    switch (drawing_state)
    {
        case 0: // 准备阶段
            // 清除电机到位标志
            clear_motor_ready_flags();
            drawing_state = 1;
            break;

        case 1: // 运动控制阶段
            uint8_t result = drawing_interpolation_control(
                target_points[current_point][0],
                target_points[current_point][1]
            );

            if (result == 1) // 运动开始
            {
                // 等待电机到位
            }
            else if (result == 0) // 等待电机到位
            {
                // 继续等待
            }
            else if (result == 2) // 错误状态
            {
                // 处理错误
                drawing_state = 2;
            }

            // 检查是否到位
            if (check_motors_ready_for_next_step() == 1)
            {
                current_point++;
                if (current_point >= sizeof(target_points)/sizeof(target_points[0]))
                {
                    drawing_state = 2; // 完成
                }
                else
                {
                    drawing_state = 0; // 准备下一个点
                }
            }
            break;

        case 2: // 完成阶段
            DEBUG_PRINTF(&huart1, "画图完成!\r\n");
            break;
    }
}
```

### 3. 状态监控

```c
// 实时监控电机状态
void monitor_motor_status(void)
{
    uint8_t x_status = get_motor_ready_status(1); // X轴状态
    uint8_t y_status = get_motor_ready_status(2); // Y轴状态

    DEBUG_PRINTF(&huart1, "电机状态 - X轴: %s, Y轴: %s\r\n",
                 x_status ? "到位" : "运动中",
                 y_status ? "到位" : "运动中");
}
```

## 关键变量说明

```c
extern uint8_t motor01_ready;   // X轴电机到位标志
extern uint8_t motor02_ready;   // Y轴电机到位标志
extern uint8_t stop_flag_car;   // 系统状态标志 (0:正常, 1:全部到位, 2:错误)
```

## 注意事项

1. **中断优先级**：确保 UART4 和 UART5 中断优先级设置合理
2. **数据帧校验**：系统会自动校验数据帧的完整性
3. **错误处理**：当检测到错误帧时，系统会设置错误标志
4. **兼容性**：保持与原有环形缓冲区和定时器任务架构的兼容性
5. **调试输出**：可通过 DEBUG_ENABLE 宏控制调试信息输出

## 调试建议

1. 使用串口 1 输出调试信息监控系统状态
2. 检查电机反馈数据帧格式是否正确
3. 确认中断服务函数正常工作
4. 验证状态机状态转换逻辑

## 扩展功能

系统预留了扩展接口，可以根据需要添加：

- 更复杂的插值算法
- 多轴电机控制
- 实时轨迹规划
- 动态速度调整
