# 调试打印控制说明

## 概述

为了减少串口输出干扰，已将项目中的所有调试打印语句暂时注释掉或禁用。本文档说明如何快速恢复这些调试打印功能。

## 修改的文件和恢复方法

### 1. app_uasrt.c
**修改内容**：
- 将 `#define DEBUG_ENABLE 1` 改为 `#define DEBUG_ENABLE 0`
- 注释了多个 `my_printf` 调用

**恢复方法**：
```c
// 将第4行改回：
#define DEBUG_ENABLE 1

// 取消注释以下函数中的打印语句：
// - process_command()
// - process_reset_command()
// - save_initial_position()
// - print_hex_array_x()
// - print_hex_array_y()
```

### 2. app_hmi.c
**修改内容**：
- 注释了所有 `my_printf` 调用

**恢复方法**：
```c
// 取消注释以下函数中的打印语句：
// - hmi_process_data()
// - hmi_process_command()
```

### 3. app_maixcam.c
**修改内容**：
- 注释了所有 `my_printf` 调用

**恢复方法**：
```c
// 取消注释以下函数中的打印语句：
// - default_laser_callback()
// - process_trajectory_command()
```

### 4. app_trajectory.c
**修改内容**：
- 添加了 `#define my_printf(...) // 注释掉所有my_printf调用`

**恢复方法**：
```c
// 删除或注释掉第5行：
// #define my_printf(...) // 注释掉所有my_printf调用

// 或者改为：
#define TRAJECTORY_DEBUG_ENABLE 1
#define my_printf(huart, format, ...)             \
    do                                               \
    {                                                \
        if (TRAJECTORY_DEBUG_ENABLE)                            \
            my_printf_real(huart, format, ##__VA_ARGS__); \
    } while (0)
```

### 5. app_pid.c
**修改内容**：
- 已经通过 `#define DEBUG_PID 0` 禁用

**恢复方法**：
```c
// 将第54行改为：
#define DEBUG_PID 1
```

### 6. Core/Src/usart.c
**修改内容**：
- 注释了UART4和UART5的调试打印
- 注释了UART3的调试打印

**恢复方法**：
```c
// 取消注释以下行：
// Line 43: my_printf(&huart1, "UART4:%02X\r\n", motor_x_rx_byte);
// Line 49: my_printf(&huart1, "UART5:%02X\r\n", motor_y_rx_byte);
// Line 73: my_printf(&huart1, "uart3:%s\r\n", hmi_rx_buf);
```

## 快速恢复所有调试打印

### 方法1：逐个文件恢复
按照上述说明逐个文件修改。

### 方法2：批量搜索替换
使用IDE的全局搜索替换功能：

1. **恢复注释的my_printf**：
   - 搜索：`//my_printf`
   - 替换：`my_printf`

2. **恢复DEBUG_ENABLE**：
   - 搜索：`#define DEBUG_ENABLE 0`
   - 替换：`#define DEBUG_ENABLE 1`

3. **恢复DEBUG_PID**：
   - 搜索：`#define DEBUG_PID 0`
   - 替换：`#define DEBUG_PID 1`

4. **删除trajectory的宏定义**：
   - 在app_trajectory.c中删除：`#define my_printf(...) // 注释掉所有my_printf调用`

## 注意事项

1. **串口冲突**：恢复调试打印后，注意串口1的输出可能会很多，影响其他功能
2. **性能影响**：大量的串口输出会影响系统性能，特别是在高频任务中
3. **缓冲区溢出**：确保串口发送缓冲区足够大，避免数据丢失
4. **选择性恢复**：建议根据需要选择性恢复某些模块的调试打印，而不是全部恢复

## 调试建议

1. **分模块调试**：一次只启用一个模块的调试打印
2. **使用条件编译**：为不同模块设置独立的调试开关
3. **降低打印频率**：在高频任务中减少打印频率
4. **使用不同串口**：将不同模块的调试信息输出到不同串口

## 当前状态

所有调试打印已被禁用，系统运行时串口输出最小化，适合正常运行和测试电机到位检测功能。
