# 步进电机编码器数据接收系统使用指南

## 概述

本指南详细说明如何使用您的步进电机编码器数据接收系统，该系统通过UART4和UART5接收X轴和Y轴电机的编码器反馈数据，实现精确的位置控制和到位检测。

## 系统架构

### 硬件连接
- **X轴电机**: 通过UART4 (115200波特率) 连接
- **Y轴电机**: 通过UART5 (115200波特率) 连接
- **调试输出**: 通过UART1输出调试信息

### 数据流向
```
步进电机编码器 → UART4/5 → 中断接收 → 状态机解析 → 到位标志设置
```

## 快速开始

### 1. 系统初始化

在您的主程序中，确保UART已正确初始化：

```c
#include "app_uasrt.h"
#include "app_motor.h"

int main(void)
{
    // HAL库初始化
    HAL_Init();
    SystemClock_Config();
    
    // 初始化UART
    MX_UART4_Init();  // X轴电机通信
    MX_UART5_Init();  // Y轴电机通信
    MX_USART1_UART_Init(); // 调试输出
    
    // 初始化电机
    Motor_Init();
    
    // 启动中断接收
    HAL_UART_Receive_IT(&huart4, &motor_x_rx_byte, 1);
    HAL_UART_Receive_IT(&huart5, &motor_y_rx_byte, 1);
    
    while(1)
    {
        // 主循环
        your_main_loop();
    }
}
```

### 2. 基本使用流程

#### 步骤1: 发送电机控制命令
```c
// 设置电机位置 (脉冲数)
Motor_Set_Position(1000, 2000);  // X轴1000脉冲，Y轴2000脉冲
```

#### 步骤2: 等待电机到位
```c
// 方法1: 轮询检查
while(1)
{
    uint8_t status = check_motors_ready_for_next_step();
    if(status == 1)
    {
        printf("所有电机已到位！\n");
        break;
    }
    else if(status == 2)
    {
        printf("检测到错误！\n");
        break;
    }
    HAL_Delay(10); // 避免过度轮询
}
```

#### 步骤3: 清除到位标志，准备下次运动
```c
clear_motor_ready_flags();
```

## 详细API说明

### 核心函数

#### 1. 电机到位状态检查
```c
uint8_t check_motors_ready_for_next_step(void);
```
**返回值:**
- `1`: 所有电机到位，可以进行下一步
- `0`: 电机未到位，需要继续等待
- `2`: 检测到错误状态

#### 2. 清除到位标志
```c
void clear_motor_ready_flags(void);
```
**用途:** 在开始新的运动前，清除之前的到位标志

#### 3. 获取单个电机状态
```c
uint8_t get_motor_ready_status(uint8_t motor_id);
```
**参数:**
- `motor_id`: 1=X轴电机, 2=Y轴电机
**返回值:** 1=已到位, 0=未到位

#### 4. 画图插值运动控制
```c
uint8_t drawing_interpolation_control(float target_x, float target_y);
```
**用途:** 专门用于画图时的精确插值运动控制

## 实际应用示例

### 示例1: 简单的点到点运动
```c
void move_to_point(int32_t x_pulses, int32_t y_pulses)
{
    // 清除之前的到位标志
    clear_motor_ready_flags();
    
    // 发送运动命令
    Motor_Set_Position(x_pulses, y_pulses);
    
    // 等待到位
    while(1)
    {
        uint8_t status = check_motors_ready_for_next_step();
        if(status == 1)
        {
            my_printf(&huart1, "到达目标位置: X=%d, Y=%d\r\n", x_pulses, y_pulses);
            break;
        }
        else if(status == 2)
        {
            my_printf(&huart1, "运动过程中发生错误！\r\n");
            break;
        }
        HAL_Delay(10);
    }
}
```

### 示例2: 多点连续运动
```c
void execute_trajectory(void)
{
    // 定义轨迹点
    int32_t trajectory[][2] = {
        {1000, 1000},
        {2000, 1500},
        {3000, 2000},
        {2000, 2500},
        {1000, 2000}
    };
    
    int num_points = sizeof(trajectory) / sizeof(trajectory[0]);
    
    for(int i = 0; i < num_points; i++)
    {
        my_printf(&huart1, "移动到点 %d: X=%d, Y=%d\r\n", 
                  i+1, trajectory[i][0], trajectory[i][1]);
        
        // 移动到下一个点
        move_to_point(trajectory[i][0], trajectory[i][1]);
        
        // 可选：在每个点停留一段时间
        HAL_Delay(500);
    }
    
    my_printf(&huart1, "轨迹执行完成！\r\n");
}
```

### 示例3: 实时状态监控
```c
void monitor_motor_status(void)
{
    uint8_t x_status = get_motor_ready_status(1); // X轴状态
    uint8_t y_status = get_motor_ready_status(2); // Y轴状态
    
    my_printf(&huart1, "电机状态 - X轴: %s, Y轴: %s\r\n",
              x_status ? "到位" : "运动中",
              y_status ? "到位" : "运动中");
}
```

## 高级功能

### 画图应用示例
```c
void drawing_example(void)
{
    float drawing_points[][2] = {
        {10.5, 20.3},
        {30.7, 40.1},
        {50.2, 60.8}
    };
    
    int num_points = sizeof(drawing_points) / sizeof(drawing_points[0]);
    
    for(int i = 0; i < num_points; i++)
    {
        uint8_t result = drawing_interpolation_control(
            drawing_points[i][0], 
            drawing_points[i][1]
        );
        
        switch(result)
        {
            case 1: // 运动开始
                my_printf(&huart1, "开始移动到: X=%.2f, Y=%.2f\r\n", 
                          drawing_points[i][0], drawing_points[i][1]);
                break;
                
            case 0: // 等待电机到位
                my_printf(&huart1, "等待电机到位...\r\n");
                i--; // 重试当前点
                HAL_Delay(10);
                break;
                
            case 2: // 错误状态
                my_printf(&huart1, "检测到错误，停止画图！\r\n");
                return;
        }
    }
}
```

## 调试和故障排除

### 1. 启用调试输出
在 `app_uasrt.c` 中修改：
```c
#define DEBUG_ENABLE 1  // 改为1启用调试
```

### 2. 常见问题

**问题1: 电机不响应**
- 检查UART连接和波特率设置
- 确认电机地址配置正确
- 验证中断是否正常工作

**问题2: 到位检测不准确**
- 检查数据帧格式是否正确
- 验证编码器反馈数据
- 确认状态机状态转换逻辑

**问题3: 系统报错**
- 查看 `stop_flag_car` 变量状态
- 检查数据帧校验是否通过
- 验证中断优先级设置

### 3. 调试命令
通过UART1发送以下命令进行调试：
- `reset` - 返回初始位置
- `set(x,y)` - 设置目标位置

## 注意事项

1. **中断优先级**: 确保UART4和UART5中断优先级设置合理
2. **数据帧校验**: 系统会自动校验数据帧完整性
3. **错误处理**: 检测到错误帧时会设置错误标志
4. **兼容性**: 保持与原有环形缓冲区架构的兼容性
5. **调试输出**: 可通过DEBUG_ENABLE宏控制调试信息

## 扩展功能

系统预留了扩展接口，可根据需要添加：
- 更复杂的插值算法
- 多轴电机控制
- 实时轨迹规划  
- 动态速度调整
- 位置反馈闭环控制

---

## 完整的主循环示例

### 基于状态机的主控制循环
```c
typedef enum {
    SYSTEM_IDLE,
    SYSTEM_MOVING,
    SYSTEM_WAITING,
    SYSTEM_ERROR
} system_state_t;

void main_control_loop(void)
{
    static system_state_t current_state = SYSTEM_IDLE;
    static uint32_t move_start_time = 0;
    static const uint32_t MOVE_TIMEOUT_MS = 5000; // 5秒超时

    switch(current_state)
    {
        case SYSTEM_IDLE:
            // 等待新的运动命令
            if(new_move_command_received())
            {
                clear_motor_ready_flags();
                execute_move_command();
                move_start_time = HAL_GetTick();
                current_state = SYSTEM_MOVING;
            }
            break;

        case SYSTEM_MOVING:
            {
                uint8_t status = check_motors_ready_for_next_step();
                if(status == 1)
                {
                    my_printf(&huart1, "运动完成！\r\n");
                    current_state = SYSTEM_IDLE;
                }
                else if(status == 2)
                {
                    my_printf(&huart1, "运动错误！\r\n");
                    current_state = SYSTEM_ERROR;
                }
                else if((HAL_GetTick() - move_start_time) > MOVE_TIMEOUT_MS)
                {
                    my_printf(&huart1, "运动超时！\r\n");
                    current_state = SYSTEM_ERROR;
                }
            }
            break;

        case SYSTEM_ERROR:
            // 错误处理
            Motor_Stop(); // 停止所有电机
            clear_motor_ready_flags();
            my_printf(&huart1, "系统进入错误状态，请检查！\r\n");
            HAL_Delay(1000);
            current_state = SYSTEM_IDLE; // 重置到空闲状态
            break;
    }
}
```

### 中断安全的状态查询
```c
// 在中断中安全地读取电机状态
uint8_t safe_check_motor_status(void)
{
    uint8_t status;

    // 临时禁用中断，确保读取的一致性
    __disable_irq();
    status = check_motors_ready_for_next_step();
    __enable_irq();

    return status;
}
```

## 性能优化建议

### 1. 减少轮询频率
```c
// 使用定时器而不是连续轮询
void setup_status_check_timer(void)
{
    // 每50ms检查一次状态，而不是连续检查
    multiTimerStart(&status_check_timer, 50, status_check_callback, NULL);
}

void status_check_callback(MultiTimer *timer, void *userData)
{
    uint8_t status = check_motors_ready_for_next_step();
    if(status != 0)
    {
        // 处理状态变化
        handle_motor_status_change(status);
    }

    // 重新启动定时器
    multiTimerStart(timer, 50, status_check_callback, NULL);
}
```

### 2. 批量处理多个运动命令
```c
typedef struct {
    int32_t x_pulses;
    int32_t y_pulses;
    uint32_t delay_ms;
} move_command_t;

void execute_move_sequence(move_command_t *commands, int count)
{
    for(int i = 0; i < count; i++)
    {
        my_printf(&huart1, "执行命令 %d/%d\r\n", i+1, count);

        // 清除标志
        clear_motor_ready_flags();

        // 发送运动命令
        Motor_Set_Position(commands[i].x_pulses, commands[i].y_pulses);

        // 等待完成
        while(check_motors_ready_for_next_step() == 0)
        {
            HAL_Delay(10);
        }

        // 可选延时
        if(commands[i].delay_ms > 0)
        {
            HAL_Delay(commands[i].delay_ms);
        }
    }
}
```

## 错误恢复机制

### 自动重试机制
```c
uint8_t move_with_retry(int32_t x_pulses, int32_t y_pulses, uint8_t max_retries)
{
    for(uint8_t retry = 0; retry < max_retries; retry++)
    {
        if(retry > 0)
        {
            my_printf(&huart1, "重试第 %d 次...\r\n", retry);
            HAL_Delay(1000); // 重试前等待
        }

        clear_motor_ready_flags();
        Motor_Set_Position(x_pulses, y_pulses);

        uint32_t start_time = HAL_GetTick();
        while(1)
        {
            uint8_t status = check_motors_ready_for_next_step();
            if(status == 1)
            {
                return 1; // 成功
            }
            else if(status == 2)
            {
                break; // 错误，尝试重试
            }
            else if((HAL_GetTick() - start_time) > 5000)
            {
                break; // 超时，尝试重试
            }
            HAL_Delay(10);
        }
    }

    return 0; // 所有重试都失败
}
```

## 配置参数说明

### 关键宏定义
```c
// 在 app_uasrt.c 中的重要配置
#define MOTOR_RX_BUFFER_SIZE 10    // 接收缓冲区大小
#define MOTOR_START_BYTE_01 0x01   // X轴起始字节
#define MOTOR_START_BYTE_02 0x02   // Y轴起始字节
#define MOTOR_END_BYTE 0x6B        // 帧尾字节
#define MOTOR_READY_FLAG_1 0xFD    // 到位标识1
#define MOTOR_READY_FLAG_2 0x9F    // 到位标识2
```

### 电机参数配置
```c
// 在 app_motor.h 中的配置
#define MOTOR_X_ADDR 0x01          // X轴电机地址
#define MOTOR_Y_ADDR 0x01          // Y轴电机地址
#define MOTOR_MAX_SPEED 20         // 最大转速(RPM)
#define MOTOR_PUL_SPEED 200        // 脉冲模式转速(RPM)
#define MOTOR_ACCEL 0              // 加速度(0表示直接启动)
#define MOTOR_MAX_ANGLE 50         // 最大角度限制(±50度)
```

**技术支持**: 如有问题，请查看调试输出或联系技术支持团队。
