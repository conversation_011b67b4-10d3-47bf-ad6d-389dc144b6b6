# 编码器系统移植文件清单和代码模板

## 📁 完整文件清单

### 🔴 核心必需文件 (必须移植)

| 文件路径 | 文件大小 | 功能描述 | 移植优先级 |
|----------|----------|----------|------------|
| `App/app_uasrt.c` | ~15KB | 编码器数据处理核心逻辑 | ⭐⭐⭐⭐⭐ |
| `App/app_uasrt.h` | ~1KB | 函数声明和接口定义 | ⭐⭐⭐⭐⭐ |
| `App/mydefine.h` | ~2KB | 系统宏定义和配置 | ⭐⭐⭐⭐⭐ |

### 🟡 UART相关文件 (需要修改)

| 文件路径 | 修改内容 | 功能描述 |
|----------|----------|----------|
| `Core/Src/usart.c` | 添加中断回调 | UART中断处理 |
| `Core/Inc/usart.h` | 添加变量声明 | UART句柄和变量 |

### 🟢 可选扩展文件

| 文件路径 | 文件大小 | 功能描述 | 移植优先级 |
|----------|----------|----------|------------|
| `App/app_motor.c` | ~8KB | 高级电机控制 | ⭐⭐⭐ |
| `App/app_motor.h` | ~1KB | 电机控制接口 | ⭐⭐⭐ |
| `Components/motor/Emm_V5.c` | ~12KB | EmmV5电机驱动 | ⭐⭐⭐ |
| `Components/motor/Emm_V5.h` | ~2KB | 电机驱动接口 | ⭐⭐⭐ |
| `Components/MultiTimer/` | ~3KB | 多定时器库 | ⭐⭐ |
| `Components/rt_ringbuffer/` | ~2KB | 环形缓冲区 | ⭐⭐ |

## 📝 代码模板

### 模板1: mydefine.h (必需配置)

```c
#ifndef __MYDEFINE_H_
#define __MYDEFINE_H_

/* ========== 基础包含文件 ========== */
#include "main.h"
#include "usart.h"
#include <stdio.h>
#include <stdarg.h>
#include <string.h>

/* ========== UART句柄定义 ========== */
// 根据您的项目修改UART句柄名称
#define MOTOR_X_UART huart4    // X轴电机UART
#define MOTOR_Y_UART huart5    // Y轴电机UART

/* ========== 电机地址配置 ========== */
#define MOTOR_X_ADDR 0x01      // X轴电机地址
#define MOTOR_Y_ADDR 0x01      // Y轴电机地址

/* ========== 电机参数配置 ========== */
#define MOTOR_MAX_SPEED 20     // 最大转速(RPM)
#define MOTOR_PUL_SPEED 200    // 脉冲模式转速(RPM)
#define MOTOR_ACCEL 0          // 加速度(0表示直接启动)
#define MOTOR_SYNC_FLAG true   // 同步标志
#define MOTOR_MAX_ANGLE 50     // 最大角度限制(±50度)

/* ========== 超时配置 ========== */
#define EMM_UART_TIMEOUT 100   // UART超时时间(ms)

/* ========== 可选功能包含 ========== */
// 如果使用多定时器，取消注释下面一行
// #include "MultiTimer.h"

// 如果使用环形缓冲区，取消注释下面一行
// #include "rt_ringbuffer.h"

#endif /* __MYDEFINE_H_ */
```

### 模板2: usart.c 中断回调修改

```c
/* ========== 在usart.c文件顶部添加 ========== */
#include "app_uasrt.h"

/* ========== 全局变量定义 ========== */
uint8_t motor_x_rx_byte = 0;  // X轴接收缓冲
uint8_t motor_y_rx_byte = 0;  // Y轴接收缓冲

/* ========== 修改或添加中断回调函数 ========== */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    /* X轴电机数据处理 */
    if (huart->Instance == UART4)
    {
        Motor_X_Receive_Data(motor_x_rx_byte);
        HAL_UART_Receive_IT(&huart4, &motor_x_rx_byte, 1);
    }
    /* Y轴电机数据处理 */
    else if (huart->Instance == UART5)
    {
        Motor_Y_Receive_Data(motor_y_rx_byte);
        HAL_UART_Receive_IT(&huart5, &motor_y_rx_byte, 1);
    }
    
    /* ========== 您的其他UART处理代码 ========== */
    // 在这里添加您原有的UART中断处理逻辑
    
}
```

### 模板3: usart.h 变量声明

```c
/* ========== 在usart.h文件中添加 ========== */

/* 编码器接收缓冲区变量声明 */
extern uint8_t motor_x_rx_byte;
extern uint8_t motor_y_rx_byte;

/* 如果需要，也可以声明UART句柄 */
extern UART_HandleTypeDef huart4;
extern UART_HandleTypeDef huart5;
```

### 模板4: main.c 初始化代码

```c
/* ========== 在main.c中添加包含 ========== */
#include "app_uasrt.h"

int main(void)
{
    /* ========== HAL库初始化 ========== */
    HAL_Init();
    SystemClock_Config();
    
    /* ========== 外设初始化 ========== */
    MX_GPIO_Init();
    MX_USART1_UART_Init();  // 调试输出
    MX_UART4_Init();        // X轴电机
    MX_UART5_Init();        // Y轴电机
    
    /* ========== 编码器系统初始化 ========== */
    // 启动UART中断接收
    HAL_UART_Receive_IT(&huart4, &motor_x_rx_byte, 1);
    HAL_UART_Receive_IT(&huart5, &motor_y_rx_byte, 1);
    
    // 清除编码器标志
    clear_motor_ready_flags();
    
    // 调试输出
    my_printf(&huart1, "编码器系统初始化完成!\r\n");
    
    /* ========== 主循环 ========== */
    while (1)
    {
        /* 您的主要应用逻辑 */
        your_application_code();
        
        /* 可选：定期检查电机状态 */
        // check_motor_status_periodically();
        
        HAL_Delay(10);
    }
}
```

### 模板5: 基础测试函数

```c
/* ========== 编码器系统测试函数 ========== */
void test_encoder_system(void)
{
    my_printf(&huart1, "\r\n=== 编码器系统测试开始 ===\r\n");
    
    // 1. 测试状态读取
    uint8_t x_status = get_motor_ready_status(1);
    uint8_t y_status = get_motor_ready_status(2);
    uint8_t overall_status = check_motors_ready_for_next_step();
    
    my_printf(&huart1, "初始状态 - X轴:%s, Y轴:%s, 整体:%d\r\n",
              x_status ? "到位" : "未到位",
              y_status ? "到位" : "未到位",
              overall_status);
    
    // 2. 测试标志清除
    clear_motor_ready_flags();
    my_printf(&huart1, "标志清除完成\r\n");
    
    // 3. 再次检查状态
    x_status = get_motor_ready_status(1);
    y_status = get_motor_ready_status(2);
    overall_status = check_motors_ready_for_next_step();
    
    my_printf(&huart1, "清除后状态 - X轴:%s, Y轴:%s, 整体:%d\r\n",
              x_status ? "到位" : "未到位",
              y_status ? "到位" : "未到位",
              overall_status);
    
    my_printf(&huart1, "=== 编码器系统测试完成 ===\r\n\r\n");
}
```

## 🔧 移植配置检查表

### STM32CubeMX配置检查

#### UART4配置 (X轴电机)
```
Parameter Settings:
- Baud Rate: 115200 Bits/s
- Word Length: 8 Bits
- Parity: None
- Stop Bits: 1
- Data Direction: Receive and Transmit
- Over Sampling: 16 Samples

NVIC Settings:
- UART4 global interrupt: Enabled
- Priority: 5 (可调整)
```

#### UART5配置 (Y轴电机)
```
Parameter Settings:
- Baud Rate: 115200 Bits/s
- Word Length: 8 Bits
- Parity: None
- Stop Bits: 1
- Data Direction: Receive and Transmit
- Over Sampling: 16 Samples

NVIC Settings:
- UART5 global interrupt: Enabled
- Priority: 5 (可调整)
```

### 编译器配置

#### Include Paths添加
```
# 在您的IDE中添加以下包含路径
App/
Components/motor/
Components/MultiTimer/    # 如果使用
Components/rt_ringbuffer/ # 如果使用
```

#### 预处理器定义
```c
// 如果需要，可以添加以下宏定义
USE_HAL_DRIVER
STM32F4xx    // 根据您的MCU型号调整
```

## 🚨 移植注意事项

### 1. UART句柄名称
确保mydefine.h中的UART句柄名称与您项目中的实际名称一致：
```c
// 检查您的main.h或usart.h中的句柄名称
extern UART_HandleTypeDef huart4;  // 确认名称
extern UART_HandleTypeDef huart5;  // 确认名称
```

### 2. 中断优先级
避免中断优先级冲突：
```c
// 在stm32f4xx_hal_msp.c中设置
HAL_NVIC_SetPriority(UART4_IRQn, 5, 0);  // 优先级可调整
HAL_NVIC_SetPriority(UART5_IRQn, 5, 0);  // 优先级可调整
```

### 3. 内存使用
编码器系统使用的内存：
```c
// 主要内存使用
uint8_t motor_x_rx_buffer[10];     // 40字节
uint8_t motor_y_rx_buffer[10];     // 40字节
// 加上状态变量约100字节总内存使用
```

### 4. 时钟配置
确保UART时钟正确配置：
```c
// 检查您的时钟配置是否支持115200波特率
// 通常需要APB1/APB2时钟频率足够高
```

## 📋 移植验证步骤

### 步骤1: 编译验证
- [ ] 无编译错误
- [ ] 无链接错误
- [ ] 无警告信息

### 步骤2: 运行时验证
- [ ] 系统正常启动
- [ ] UART中断正常工作
- [ ] 调试输出正常

### 步骤3: 功能验证
- [ ] 状态检查函数返回正确值
- [ ] 标志清除功能正常
- [ ] 中断接收数据正常

---

**移植支持**: 按照此清单和模板进行移植，可确保编码器系统正确集成到您的项目中。
