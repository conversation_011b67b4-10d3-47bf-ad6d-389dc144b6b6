# 编码器系统最小移植包

## 🎯 最小移植目标

如果您只需要**最基础的编码器读取功能**，这个最小移植包包含了绝对必需的文件和代码，让您用最少的工作量获得编码器数据接收能力。

## 📦 最小文件包 (仅3个文件)

### 文件1: `encoder_reader.h` (新建)
```c
#ifndef __ENCODER_READER_H_
#define __ENCODER_READER_H_

#include "main.h"
#include "usart.h"
#include <stdint.h>

/* ========== 配置参数 ========== */
// 根据您的项目修改UART句柄名称
#define ENCODER_X_UART huart4
#define ENCODER_Y_UART huart5

/* ========== 数据帧定义 ========== */
#define FRAME_START_BYTE    0x01
#define FRAME_READY_FLAG1   0xFD
#define FRAME_READY_FLAG2   0x9F
#define FRAME_END_BYTE      0x6B
#define FRAME_BUFFER_SIZE   10

/* ========== 全局变量 ========== */
extern uint8_t encoder_x_ready;    // X轴到位标志
extern uint8_t encoder_y_ready;    // Y轴到位标志
extern uint8_t encoder_error;      // 错误标志

/* ========== 核心函数 ========== */
void encoder_init(void);                           // 初始化编码器系统
void encoder_x_process_byte(uint8_t byte);         // 处理X轴数据
void encoder_y_process_byte(uint8_t byte);         // 处理Y轴数据
uint8_t encoder_check_ready(void);                 // 检查到位状态
void encoder_clear_flags(void);                    // 清除标志

/* ========== 便利函数 ========== */
uint8_t encoder_wait_ready(uint32_t timeout_ms);   // 等待到位(带超时)
void encoder_print_status(void);                   // 打印状态(调试用)

#endif /* __ENCODER_READER_H_ */
```

### 文件2: `encoder_reader.c` (新建)
```c
#include "encoder_reader.h"
#include <stdio.h>

/* ========== 全局变量定义 ========== */
uint8_t encoder_x_ready = 0;
uint8_t encoder_y_ready = 0;
uint8_t encoder_error = 0;

/* ========== 内部变量 ========== */
static uint8_t x_rx_buffer[FRAME_BUFFER_SIZE];
static uint8_t y_rx_buffer[FRAME_BUFFER_SIZE];
static uint8_t x_rx_state = 0;
static uint8_t y_rx_state = 0;
static uint8_t x_rx_counter = 0;
static uint8_t y_rx_counter = 0;

/* ========== UART接收缓冲 ========== */
uint8_t encoder_x_rx_byte = 0;
uint8_t encoder_y_rx_byte = 0;

/**
 * @brief 初始化编码器系统
 */
void encoder_init(void)
{
    encoder_clear_flags();
    
    // 启动UART中断接收
    HAL_UART_Receive_IT(&ENCODER_X_UART, &encoder_x_rx_byte, 1);
    HAL_UART_Receive_IT(&ENCODER_Y_UART, &encoder_y_rx_byte, 1);
    
    printf("编码器系统初始化完成\r\n");
}

/**
 * @brief 处理X轴编码器数据
 */
void encoder_x_process_byte(uint8_t byte)
{
    switch(x_rx_state)
    {
        case 0: // 等待起始字节
            if(byte == FRAME_START_BYTE)
            {
                x_rx_buffer[0] = byte;
                x_rx_counter = 1;
                x_rx_state = 1;
            }
            break;
            
        case 1: // 接收数据
            x_rx_buffer[x_rx_counter++] = byte;
            if(x_rx_counter >= FRAME_BUFFER_SIZE || byte == FRAME_END_BYTE)
            {
                x_rx_state = 2;
            }
            break;
            
        case 2: // 处理数据
            if(x_rx_buffer[x_rx_counter-1] == FRAME_END_BYTE)
            {
                // 检查到位标识
                if(x_rx_buffer[0] == FRAME_START_BYTE &&
                   x_rx_buffer[1] == FRAME_READY_FLAG1 &&
                   x_rx_buffer[2] == FRAME_READY_FLAG2)
                {
                    encoder_x_ready = 1;
                    printf("X轴到位\r\n");
                }
            }
            else
            {
                encoder_error = 1;
                printf("X轴数据错误\r\n");
            }
            
            // 重置状态
            x_rx_state = 0;
            x_rx_counter = 0;
            break;
    }
}

/**
 * @brief 处理Y轴编码器数据
 */
void encoder_y_process_byte(uint8_t byte)
{
    switch(y_rx_state)
    {
        case 0: // 等待起始字节
            if(byte == FRAME_START_BYTE)
            {
                y_rx_buffer[0] = byte;
                y_rx_counter = 1;
                y_rx_state = 1;
            }
            break;
            
        case 1: // 接收数据
            y_rx_buffer[y_rx_counter++] = byte;
            if(y_rx_counter >= FRAME_BUFFER_SIZE || byte == FRAME_END_BYTE)
            {
                y_rx_state = 2;
            }
            break;
            
        case 2: // 处理数据
            if(y_rx_buffer[y_rx_counter-1] == FRAME_END_BYTE)
            {
                // 检查到位标识
                if(y_rx_buffer[0] == FRAME_START_BYTE &&
                   y_rx_buffer[1] == FRAME_READY_FLAG1 &&
                   y_rx_buffer[2] == FRAME_READY_FLAG2)
                {
                    encoder_y_ready = 1;
                    printf("Y轴到位\r\n");
                }
            }
            else
            {
                encoder_error = 1;
                printf("Y轴数据错误\r\n");
            }
            
            // 重置状态
            y_rx_state = 0;
            y_rx_counter = 0;
            break;
    }
}

/**
 * @brief 检查编码器到位状态
 * @return 0=运动中, 1=全部到位, 2=错误
 */
uint8_t encoder_check_ready(void)
{
    if(encoder_error) return 2;
    if(encoder_x_ready && encoder_y_ready) return 1;
    return 0;
}

/**
 * @brief 清除所有标志
 */
void encoder_clear_flags(void)
{
    encoder_x_ready = 0;
    encoder_y_ready = 0;
    encoder_error = 0;
}

/**
 * @brief 等待编码器到位(带超时)
 * @param timeout_ms 超时时间(毫秒)
 * @return 1=成功到位, 0=超时或错误
 */
uint8_t encoder_wait_ready(uint32_t timeout_ms)
{
    uint32_t start_time = HAL_GetTick();
    
    while((HAL_GetTick() - start_time) < timeout_ms)
    {
        uint8_t status = encoder_check_ready();
        if(status == 1) return 1;  // 成功到位
        if(status == 2) return 0;  // 错误
        HAL_Delay(10);
    }
    
    return 0; // 超时
}

/**
 * @brief 打印编码器状态(调试用)
 */
void encoder_print_status(void)
{
    printf("编码器状态 - X:%s, Y:%s, 错误:%s\r\n",
           encoder_x_ready ? "到位" : "运动",
           encoder_y_ready ? "到位" : "运动", 
           encoder_error ? "是" : "否");
}
```

### 文件3: 修改您的 `usart.c` (添加中断处理)
```c
/* ========== 在usart.c顶部添加 ========== */
#include "encoder_reader.h"

/* ========== 修改或添加中断回调函数 ========== */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == UART4)  // X轴
    {
        encoder_x_process_byte(encoder_x_rx_byte);
        HAL_UART_Receive_IT(&huart4, &encoder_x_rx_byte, 1);
    }
    else if (huart->Instance == UART5)  // Y轴
    {
        encoder_y_process_byte(encoder_y_rx_byte);
        HAL_UART_Receive_IT(&huart5, &encoder_y_rx_byte, 1);
    }
    
    // 您的其他UART处理代码...
}
```

## 🚀 3步快速集成

### 步骤1: 复制文件
将上面的 `encoder_reader.h` 和 `encoder_reader.c` 保存到您的项目中

### 步骤2: 修改usart.c
在您的 `usart.c` 文件中添加上面的中断处理代码

### 步骤3: 在main.c中初始化
```c
#include "encoder_reader.h"

int main(void)
{
    // 您的初始化代码...
    
    encoder_init();  // 初始化编码器系统
    
    while(1)
    {
        // 您的主循环代码...
    }
}
```

## 💡 基础使用示例

### 示例1: 简单等待到位
```c
void simple_move_example(void)
{
    encoder_clear_flags();
    
    // 发送您的电机控制命令
    // your_motor_move_command(1000, 2000);
    
    // 等待到位 (最多10秒)
    if(encoder_wait_ready(10000))
    {
        printf("移动完成!\r\n");
    }
    else
    {
        printf("移动超时或错误!\r\n");
    }
}
```

### 示例2: 状态轮询
```c
void polling_example(void)
{
    encoder_clear_flags();
    
    // 发送电机命令
    // your_motor_move_command(500, 800);
    
    while(1)
    {
        uint8_t status = encoder_check_ready();
        
        if(status == 1)
        {
            printf("所有轴已到位!\r\n");
            break;
        }
        else if(status == 2)
        {
            printf("检测到错误!\r\n");
            break;
        }
        
        // 每500ms打印一次状态
        encoder_print_status();
        HAL_Delay(500);
    }
}
```

### 示例3: 单轴状态检查
```c
void single_axis_check(void)
{
    if(encoder_x_ready)
    {
        printf("X轴已到位\r\n");
    }
    
    if(encoder_y_ready)
    {
        printf("Y轴已到位\r\n");
    }
    
    if(encoder_error)
    {
        printf("检测到通信错误\r\n");
        encoder_clear_flags(); // 清除错误
    }
}
```

## ⚙️ 配置说明

### UART配置要求
- **UART4**: 115200-8-N-1, 启用接收中断
- **UART5**: 115200-8-N-1, 启用接收中断

### 内存使用
- 总RAM使用: 约50字节
- Flash使用: 约2KB

### 依赖项
- HAL库UART驱动
- printf函数 (可选，用于调试)

## 🔧 自定义配置

### 修改UART句柄
如果您的UART句柄名称不同，修改 `encoder_reader.h`:
```c
#define ENCODER_X_UART huart1  // 改为您的X轴UART
#define ENCODER_Y_UART huart2  // 改为您的Y轴UART
```

### 禁用调试输出
如果不需要printf调试信息，注释掉相关printf语句：
```c
// printf("X轴到位\r\n");  // 注释掉这行
```

### 修改数据帧格式
如果您的编码器数据帧格式不同，修改相关宏定义：
```c
#define FRAME_START_BYTE    0x02  // 修改起始字节
#define FRAME_READY_FLAG1   0xAA  // 修改到位标识
#define FRAME_READY_FLAG2   0xBB  // 修改到位标识
```

## ✅ 验证测试

### 基础功能测试
```c
void test_encoder_minimal(void)
{
    printf("=== 最小编码器系统测试 ===\r\n");
    
    // 测试初始化
    encoder_init();
    
    // 测试状态读取
    encoder_print_status();
    
    // 测试标志清除
    encoder_clear_flags();
    printf("标志已清除\r\n");
    
    // 再次检查状态
    encoder_print_status();
    
    printf("=== 测试完成 ===\r\n");
}
```

## 🎯 优势

- ✅ **最小化**: 只需3个文件，50行核心代码
- ✅ **独立性**: 不依赖其他复杂库
- ✅ **易理解**: 代码结构清晰简单
- ✅ **易移植**: 只需修改UART句柄名称
- ✅ **功能完整**: 包含所有基础编码器读取功能

---

**使用建议**: 如果您只需要基础的编码器到位检测功能，这个最小移植包是最佳选择。如果需要更高级功能，请参考完整移植指南。
