#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.Request0=USART2_RX
Dma.Request1=USART1_RX
Dma.Request2=USART3_RX
Dma.RequestsNb=3
Dma.USART1_RX.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART1_RX.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_RX.1.Instance=DMA2_Stream2
Dma.USART1_RX.1.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_RX.1.MemInc=DMA_MINC_ENABLE
Dma.USART1_RX.1.Mode=DMA_NORMAL
Dma.USART1_RX.1.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_RX.1.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_RX.1.Priority=DMA_PRIORITY_LOW
Dma.USART1_RX.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART2_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART2_RX.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART2_RX.0.Instance=DMA1_Stream5
Dma.USART2_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART2_RX.0.MemInc=DMA_MINC_ENABLE
Dma.USART2_RX.0.Mode=DMA_NORMAL
Dma.USART2_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART2_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.USART2_RX.0.Priority=DMA_PRIORITY_LOW
Dma.USART2_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART3_RX.2.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART3_RX.2.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART3_RX.2.Instance=DMA1_Stream1
Dma.USART3_RX.2.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART3_RX.2.MemInc=DMA_MINC_ENABLE
Dma.USART3_RX.2.Mode=DMA_NORMAL
Dma.USART3_RX.2.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART3_RX.2.PeriphInc=DMA_PINC_DISABLE
Dma.USART3_RX.2.Priority=DMA_PRIORITY_LOW
Dma.USART3_RX.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F407VGT6
Mcu.Family=STM32F4
Mcu.IP0=DMA
Mcu.IP1=I2C1
Mcu.IP10=TIM8
Mcu.IP11=TIM12
Mcu.IP12=UART4
Mcu.IP13=UART5
Mcu.IP14=USART1
Mcu.IP15=USART2
Mcu.IP16=USART3
Mcu.IP2=I2C2
Mcu.IP3=NVIC
Mcu.IP4=RCC
Mcu.IP5=SYS
Mcu.IP6=TIM1
Mcu.IP7=TIM2
Mcu.IP8=TIM3
Mcu.IP9=TIM4
Mcu.IPNb=17
Mcu.Name=STM32F407V(E-G)Tx
Mcu.Package=LQFP100
Mcu.Pin0=PE2
Mcu.Pin1=PE3
Mcu.Pin10=PA3
Mcu.Pin11=PA6
Mcu.Pin12=PE9
Mcu.Pin13=PE11
Mcu.Pin14=PB10
Mcu.Pin15=PB11
Mcu.Pin16=PB14
Mcu.Pin17=PD8
Mcu.Pin18=PD9
Mcu.Pin19=PD12
Mcu.Pin2=PE4
Mcu.Pin20=PC6
Mcu.Pin21=PA9
Mcu.Pin22=PA10
Mcu.Pin23=PA13
Mcu.Pin24=PA14
Mcu.Pin25=PC10
Mcu.Pin26=PC11
Mcu.Pin27=PC12
Mcu.Pin28=PD2
Mcu.Pin29=PD3
Mcu.Pin3=PE5
Mcu.Pin30=PD4
Mcu.Pin31=PD5
Mcu.Pin32=PD6
Mcu.Pin33=PD7
Mcu.Pin34=PB3
Mcu.Pin35=PB4
Mcu.Pin36=PB5
Mcu.Pin37=PB6
Mcu.Pin38=PB8
Mcu.Pin39=PB9
Mcu.Pin4=PE6
Mcu.Pin40=PE0
Mcu.Pin41=PE1
Mcu.Pin42=VP_SYS_VS_Systick
Mcu.Pin43=VP_TIM8_VS_ClockSourceINT
Mcu.Pin5=PH0-OSC_IN
Mcu.Pin6=PH1-OSC_OUT
Mcu.Pin7=PA0-WKUP
Mcu.Pin8=PA1
Mcu.Pin9=PA2
Mcu.PinsNb=44
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F407VGTx
MxCube.Version=6.14.1
MxDb.Version=DB.6.0.141
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA1_Stream1_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA1_Stream5_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream2_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.UART4_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UART5_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART2_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART3_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA0-WKUP.Locked=true
PA0-WKUP.Signal=S_TIM2_CH1_ETR
PA1.Locked=true
PA1.Signal=S_TIM2_CH2
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA2.Mode=Asynchronous
PA2.Signal=USART2_TX
PA3.Mode=Asynchronous
PA3.Signal=USART2_RX
PA6.Signal=S_TIM3_CH1
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB10.Mode=I2C
PB10.Signal=I2C2_SCL
PB11.Mode=I2C
PB11.Signal=I2C2_SDA
PB14.Signal=S_TIM12_CH1
PB3.GPIOParameters=PinState,GPIO_Label
PB3.GPIO_Label=ROW_0
PB3.Locked=true
PB3.PinState=GPIO_PIN_RESET
PB3.Signal=GPIO_Output
PB4.GPIOParameters=GPIO_Label
PB4.GPIO_Label=ROW_1
PB4.Locked=true
PB4.Signal=GPIO_Output
PB5.GPIOParameters=GPIO_Label
PB5.GPIO_Label=ROW_2
PB5.Locked=true
PB5.Signal=GPIO_Output
PB6.GPIOParameters=GPIO_Label
PB6.GPIO_Label=ROW_3
PB6.Locked=true
PB6.Signal=GPIO_Output
PB8.Locked=true
PB8.Mode=I2C
PB8.Signal=I2C1_SCL
PB9.Locked=true
PB9.Mode=I2C
PB9.Signal=I2C1_SDA
PC10.Mode=Asynchronous
PC10.Signal=UART4_TX
PC11.Mode=Asynchronous
PC11.Signal=UART4_RX
PC12.Mode=Asynchronous
PC12.Signal=UART5_TX
PC6.Locked=true
PC6.Signal=S_TIM8_CH1
PD12.Signal=S_TIM4_CH1
PD2.Mode=Asynchronous
PD2.Signal=UART5_RX
PD3.GPIOParameters=GPIO_PuPd,GPIO_Label
PD3.GPIO_Label=COL_0
PD3.GPIO_PuPd=GPIO_PULLDOWN
PD3.Locked=true
PD3.Signal=GPIO_Input
PD4.GPIOParameters=GPIO_PuPd,GPIO_Label
PD4.GPIO_Label=COL_1
PD4.GPIO_PuPd=GPIO_PULLDOWN
PD4.Locked=true
PD4.Signal=GPIO_Input
PD5.GPIOParameters=GPIO_PuPd,GPIO_Label
PD5.GPIO_Label=COL_2
PD5.GPIO_PuPd=GPIO_PULLDOWN
PD5.Locked=true
PD5.Signal=GPIO_Input
PD6.GPIOParameters=GPIO_PuPd,GPIO_Label
PD6.GPIO_Label=COL_3
PD6.GPIO_PuPd=GPIO_PULLDOWN
PD6.Locked=true
PD6.Signal=GPIO_Input
PD7.GPIOParameters=GPIO_Label
PD7.GPIO_Label=BEEP
PD7.Locked=true
PD7.Signal=GPIO_Output
PD8.Mode=Asynchronous
PD8.Signal=USART3_TX
PD9.Mode=Asynchronous
PD9.Signal=USART3_RX
PE0.GPIOParameters=PinState,GPIO_Label
PE0.GPIO_Label=LED0
PE0.Locked=true
PE0.PinState=GPIO_PIN_SET
PE0.Signal=GPIO_Output
PE1.GPIOParameters=PinState,GPIO_Label
PE1.GPIO_Label=LED1
PE1.Locked=true
PE1.PinState=GPIO_PIN_SET
PE1.Signal=GPIO_Output
PE11.Signal=S_TIM1_CH2
PE2.GPIOParameters=PinState,GPIO_Label
PE2.GPIO_Label=LED2
PE2.Locked=true
PE2.PinState=GPIO_PIN_SET
PE2.Signal=GPIO_Output
PE3.GPIOParameters=PinState,GPIO_Label
PE3.GPIO_Label=LED3
PE3.Locked=true
PE3.PinState=GPIO_PIN_SET
PE3.Signal=GPIO_Output
PE4.GPIOParameters=PinState,GPIO_Label
PE4.GPIO_Label=LED4
PE4.Locked=true
PE4.PinState=GPIO_PIN_SET
PE4.Signal=GPIO_Output
PE5.GPIOParameters=PinState,GPIO_Label
PE5.GPIO_Label=LED5
PE5.Locked=true
PE5.PinState=GPIO_PIN_SET
PE5.Signal=GPIO_Output
PE6.GPIOParameters=PinState,GPIO_Label
PE6.GPIO_Label=LED6
PE6.Locked=true
PE6.PinState=GPIO_PIN_SET
PE6.Signal=GPIO_Output
PE9.Signal=S_TIM1_CH1
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407VGTx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x800
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=project.ioc
ProjectManager.ProjectName=project
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0xF00
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_I2C1_Init-I2C1-false-HAL-true,5-MX_I2C2_Init-I2C2-false-HAL-true,6-MX_TIM1_Init-TIM1-false-HAL-true,7-MX_TIM2_Init-TIM2-false-HAL-true,8-MX_TIM3_Init-TIM3-false-HAL-true,9-MX_TIM4_Init-TIM4-false-HAL-true,10-MX_TIM8_Init-TIM8-false-HAL-true,11-MX_TIM12_Init-TIM12-false-HAL-true,12-MX_UART4_Init-UART4-false-HAL-true,13-MX_UART5_Init-UART5-false-HAL-true,14-MX_USART1_UART_Init-USART1-false-HAL-true,15-MX_USART2_UART_Init-USART2-false-HAL-true,16-MX_USART3_UART_Init-USART3-false-HAL-true
RCC.48MHZClocksFreq_Value=84000000
RCC.AHBFreq_Value=168000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=42000000
RCC.APB1TimFreq_Value=84000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=84000000
RCC.APB2TimFreq_Value=168000000
RCC.CortexFreq_Value=168000000
RCC.EthernetFreq_Value=168000000
RCC.FCLKCortexFreq_Value=168000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=168000000
RCC.HSE_VALUE=8000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=192000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSE_VALUE,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,PLLSourceVirtual,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSE_VALUE=32768
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=168000000
RCC.PLLCLKFreq_Value=168000000
RCC.PLLM=4
RCC.PLLN=168
RCC.PLLQCLKFreq_Value=84000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=4000000
RCC.SYSCLKFreq_VALUE=168000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=384000000
RCC.VCOInputFreq_Value=2000000
RCC.VCOOutputFreq_Value=336000000
RCC.VcooutputI2S=192000000
SH.S_TIM12_CH1.0=TIM12_CH1,PWM Generation1 CH1
SH.S_TIM12_CH1.ConfNb=1
SH.S_TIM1_CH1.0=TIM1_CH1,Encoder_Interface
SH.S_TIM1_CH1.ConfNb=1
SH.S_TIM1_CH2.0=TIM1_CH2,Encoder_Interface
SH.S_TIM1_CH2.ConfNb=1
SH.S_TIM2_CH1_ETR.0=TIM2_CH1,Encoder_Interface
SH.S_TIM2_CH1_ETR.ConfNb=1
SH.S_TIM2_CH2.0=TIM2_CH2,Encoder_Interface
SH.S_TIM2_CH2.ConfNb=1
SH.S_TIM3_CH1.0=TIM3_CH1,PWM Generation1 CH1
SH.S_TIM3_CH1.ConfNb=1
SH.S_TIM4_CH1.0=TIM4_CH1,PWM Generation1 CH1
SH.S_TIM4_CH1.ConfNb=1
SH.S_TIM8_CH1.0=TIM8_CH1,PWM Generation1 CH1
SH.S_TIM8_CH1.ConfNb=1
TIM12.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM12.IPParameters=Channel-PWM Generation1 CH1
TIM3.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM3.IPParameters=Channel-PWM Generation1 CH1
TIM4.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM4.IPParameters=Channel-PWM Generation1 CH1
TIM8.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM8.IPParameters=Channel-PWM Generation1 CH1
UART4.IPParameters=VirtualMode
UART4.VirtualMode=Asynchronous
UART5.IPParameters=VirtualMode
UART5.VirtualMode=Asynchronous
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
USART2.IPParameters=VirtualMode
USART2.VirtualMode=VM_ASYNC
USART3.IPParameters=VirtualMode
USART3.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM8_VS_ClockSourceINT.Mode=Internal
VP_TIM8_VS_ClockSourceINT.Signal=TIM8_VS_ClockSourceINT
board=custom
